/**
 * GamePort - 主JavaScript文件
 * 处理网站的交互功能
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initMobileMenu();
    initGameCards();
    initScrollEffects();
    initDisabledNavigation();

    console.log('GamePort website initialized successfully!');
});

/**
 * 移动端菜单功能
 */
function initMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.getElementById('navMenu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // 切换汉堡菜单图标动画
            const hamburgerLines = mobileMenuToggle.querySelectorAll('.hamburger-line');
            hamburgerLines.forEach((line, index) => {
                if (navMenu.classList.contains('active')) {
                    if (index === 0) line.style.transform = 'rotate(45deg) translate(5px, 5px)';
                    if (index === 1) line.style.opacity = '0';
                    if (index === 2) line.style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    line.style.transform = '';
                    line.style.opacity = '';
                }
            });
        });
        
        // 点击菜单项时关闭移动菜单
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    navMenu.classList.remove('active');
                    const hamburgerLines = mobileMenuToggle.querySelectorAll('.hamburger-line');
                    hamburgerLines.forEach(line => {
                        line.style.transform = '';
                        line.style.opacity = '';
                    });
                }
            });
        });
    }
}

/**
 * 游戏卡片交互效果
 */
function initGameCards() {
    const gameCards = document.querySelectorAll('.game-card');

    gameCards.forEach(card => {
        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            const thumbnail = this.querySelector('.game-thumbnail');
            const title = this.querySelector('.game-title');

            // 卡片整体效果
            this.style.transform = 'translateY(-4px)';
            this.style.boxShadow = 'var(--shadow-lg)';

            // 图片缩放效果
            if (thumbnail) {
                thumbnail.style.transform = 'scale(1.05)';
            }

            // 标题颜色变化
            if (title) {
                title.style.color = 'var(--primary-color)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const thumbnail = this.querySelector('.game-thumbnail');
            const title = this.querySelector('.game-title');

            // 重置所有效果
            this.style.transform = '';
            this.style.boxShadow = '';

            if (thumbnail) {
                thumbnail.style.transform = '';
            }

            if (title) {
                title.style.color = '';
            }
        });

        // 点击效果
        card.addEventListener('click', function(e) {
            // 添加点击动画
            this.style.transform = 'translateY(-2px) scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

/**
 * 滚动效果
 */
function initScrollEffects() {
    // 返回顶部按钮
    createBackToTopButton();
    
    // 滚动时的header效果
    let lastScrollTop = 0;
    const header = document.querySelector('.site-header');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Header阴影效果
        if (scrollTop > 10) {
            header.style.boxShadow = 'var(--shadow-lg)';
        } else {
            header.style.boxShadow = 'var(--shadow-sm)';
        }
        
        lastScrollTop = scrollTop;
    });
}

/**
 * 创建返回顶部按钮
 */
function createBackToTopButton() {
    const backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '↑';
    backToTopBtn.className = 'back-to-top-btn';
    backToTopBtn.style.cssText = `
        position: fixed;
        bottom: 140px;
        right: 30px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        border: none;
        font-size: 20px;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: var(--shadow-lg);
    `;
    
    document.body.appendChild(backToTopBtn);
    
    // 显示/隐藏按钮
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.opacity = '1';
            backToTopBtn.style.visibility = 'visible';
        } else {
            backToTopBtn.style.opacity = '0';
            backToTopBtn.style.visibility = 'hidden';
        }
    });
    
    // 点击返回顶部
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // 悬停效果
    backToTopBtn.addEventListener('mouseenter', function() {
        this.style.backgroundColor = 'var(--primary-hover)';
        this.style.transform = 'scale(1.1)';
    });
    
    backToTopBtn.addEventListener('mouseleave', function() {
        this.style.backgroundColor = 'var(--primary-color)';
        this.style.transform = 'scale(1)';
    });
}

/**
 * 图片懒加载功能
 */
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

/**
 * 工具函数：防抖
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 工具函数：节流
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 游戏分享功能
 */
function shareGame() {
    const gameTitle = document.querySelector('.game-title-large')?.textContent || 'Awesome Game';
    const gameUrl = window.location.href;

    // 检查是否支持Web Share API
    if (navigator.share) {
        navigator.share({
            title: `Play ${gameTitle} Online`,
            text: `Check out this awesome game: ${gameTitle}`,
            url: gameUrl
        }).then(() => {
            console.log('Game shared successfully');
        }).catch((error) => {
            console.log('Error sharing game:', error);
            fallbackShare(gameTitle, gameUrl);
        });
    } else {
        fallbackShare(gameTitle, gameUrl);
    }
}

/**
 * 备用分享方法（复制链接）
 */
function fallbackShare(gameTitle, gameUrl) {
    // 复制链接到剪贴板
    if (navigator.clipboard) {
        navigator.clipboard.writeText(gameUrl).then(() => {
            showShareNotification('Link copied to clipboard!');
        }).catch(() => {
            showShareDialog(gameTitle, gameUrl);
        });
    } else {
        showShareDialog(gameTitle, gameUrl);
    }
}

/**
 * 显示分享对话框
 */
function showShareDialog(gameTitle, gameUrl) {
    const shareText = `Check out ${gameTitle}: ${gameUrl}`;
    prompt('Copy this link to share:', shareText);
}

/**
 * 显示分享通知
 */
function showShareNotification(message) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'share-notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-weight: 500;
        animation: slideIn 0.3s ease-out;
    `;

    // 添加动画样式
    if (!document.querySelector('#shareNotificationStyles')) {
        const style = document.createElement('style');
        style.id = 'shareNotificationStyles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // 3秒后移除通知
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * 初始化所有功能
 */
document.addEventListener('DOMContentLoaded', function() {
    initMobileMenu();
    initGameCards();
    initLazyLoading();

    console.log('🎮 GamePort website initialized successfully!');
});

/**
 * 游戏加载功能 - 优化版本
 */
function startGame() {
    console.log('🎮 Starting game...');

    // 获取预览和iframe元素
    const preview = document.getElementById('gamePreview');
    const iframe = document.getElementById('gameframe');

    if (!preview || !iframe) {
        console.error('❌ Game elements not found');
        return;
    }

    // 隐藏预览界面
    preview.style.display = 'none';

    // 显示游戏iframe
    iframe.style.display = 'block';

    // 从data-src加载实际游戏URL
    const gameUrl = iframe.getAttribute('data-src');
    if (gameUrl && gameUrl !== 'about:blank') {
        iframe.src = gameUrl;
        console.log('✅ Game loaded:', gameUrl);
    }

    // 移动端自动全屏
    if (isMobile()) {
        setTimeout(() => {
            requestGameFullscreen(iframe);
        }, 500); // 延迟500ms确保iframe完全加载
    }
}

/**
 * 检测是否为移动设备
 */
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
           || window.innerWidth <= 768;
}

/**
 * 请求游戏全屏（移动端）
 */
function requestGameFullscreen(element) {
    if (!element) return;

    try {
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
        console.log('📱 Mobile fullscreen requested');
    } catch (error) {
        console.warn('⚠️ Fullscreen request failed:', error);
    }
}

/**
 * 兼容旧版本的playGame函数
 */
function playGame(gameUrl) {
    console.warn('⚠️ playGame() is deprecated, use startGame() instead');
    startGame();
}

/**
 * 全屏功能 - 优化版本
 */
function toggleFullscreen() {
    const gameIframe = document.getElementById('gameframe');

    if (!gameIframe) {
        alert('请先开始游戏再使用全屏功能');
        return;
    }

    // 检查游戏是否已经开始
    if (gameIframe.style.display === 'none' || gameIframe.src === 'about:blank') {
        alert('请先开始游戏再使用全屏功能');
        return;
    }

    requestGameFullscreen(gameIframe);
}

// 更新主初始化函数
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initMobileMenu();
    initGameCards();
    initScrollEffects();

    // 初始化Feature折叠功能
    new FeatureCollapse();

    console.log('🎮 GamePort website initialized successfully!');
});/*
*
 * 游戏搜索功能类
 */
class GameSearch {
    constructor() {
        this.games = window.GAMES_DATA || [];
        this.searchInput = document.getElementById('searchInput');
        this.searchBtn = document.getElementById('searchBtn');
        this.resultsContainer = null;
        this.selectedIndex = -1;
        this.currentResults = [];
        
        this.init();
    }
    
    init() {
        if (!this.searchInput) return;
        
        this.createResultsContainer();
        this.bindEvents();
        
        console.log('🔍 Search initialized with', this.games.length, 'games');
    }
    
    createResultsContainer() {
        this.resultsContainer = document.createElement('div');
        this.resultsContainer.className = 'search-results';
        this.searchInput.parentElement.appendChild(this.resultsContainer);
    }
    
    bindEvents() {
        // 移动端搜索按钮点击事件
        if (this.searchBtn) {
            this.searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleMobileSearch();
            });
        }

        // 输入事件（带防抖）
        let debounceTimer;
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                this.handleSearch(e.target.value);
            }, 200);
        });
        
        // 键盘导航
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // 失焦隐藏结果
        this.searchInput.addEventListener('blur', () => {
            setTimeout(() => this.hideResults(), 100);
        });
        
        // 聚焦显示结果
        this.searchInput.addEventListener('focus', () => {
            if (this.currentResults.length > 0) {
                this.showResults();
            }
        });
        
        // 点击外部隐藏结果和移动端搜索框
        document.addEventListener('click', (e) => {
            if (!this.searchInput.parentElement.contains(e.target)) {
                this.hideResults();
                // 移动端：点击外部隐藏搜索框
                if (window.innerWidth <= 480 && this.searchInput.classList.contains('active')) {
                    this.searchInput.classList.remove('active');
                }
            }
        });
    }

    toggleMobileSearch() {
        // 检查是否为移动端（屏幕宽度小于480px）
        if (window.innerWidth > 480) return;

        const isActive = this.searchInput.classList.contains('active');

        if (isActive) {
            // 隐藏搜索框
            this.searchInput.classList.remove('active');
            this.searchInput.blur();
            this.hideResults();
        } else {
            // 显示搜索框并聚焦
            this.searchInput.classList.add('active');
            setTimeout(() => {
                this.searchInput.focus();
            }, 100);
        }
    }

    handleSearch(query) {
        if (query.length < 2) {
            this.hideResults();
            return;
        }
        
        this.currentResults = this.fuzzySearch(query);
        this.selectedIndex = -1;
        
        if (this.currentResults.length > 0) {
            this.renderResults(this.currentResults, query);
            this.showResults();
        } else {
            this.renderNoResults();
            this.showResults();
        }
    }
    
    fuzzySearch(query) {
        const normalizedQuery = query.toLowerCase().trim();
        
        return this.games
            .map(game => ({
                ...game,
                similarity: this.calculateSimilarity(normalizedQuery, game.name.toLowerCase())
            }))
            .filter(game => game.similarity > 0.3)
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, 8);
    }
    
    calculateSimilarity(query, target) {
        // 精确匹配得分最高
        if (target.includes(query)) {
            const ratio = query.length / target.length;
            return 0.8 + (ratio * 0.2);
        }
        
        // 使用简化的编辑距离算法
        const distance = this.levenshteinDistance(query, target);
        const maxLength = Math.max(query.length, target.length);
        return Math.max(0, 1 - (distance / maxLength));
    }
    
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    renderResults(results, query) {
        const html = results.map((game, index) => `
            <div class="search-result-item" data-index="${index}" data-url="${game.url}">
                <img src="${game.thumbnail}" alt="${game.name}" class="search-result-thumbnail" loading="lazy">
                <div class="search-result-info">
                    <div class="search-result-name">${this.highlightMatch(game.name, query)}</div>
                    <div class="search-result-category">${game.category} game</div>
                </div>
            </div>
        `).join('');
        
        this.resultsContainer.innerHTML = html;
        
        // 绑定点击事件
        this.resultsContainer.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                // 立即添加加载状态
                item.style.opacity = '0.6';
                item.style.pointerEvents = 'none';
                
                // 立即跳转
                this.navigateToGame(item.dataset.url);
            });
        });
    }
    
    renderNoResults() {
        this.resultsContainer.innerHTML = `
            <div class="search-no-results">
                <p>No games found. Try a different search term.</p>
            </div>
        `;
    }
    
    highlightMatch(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }
    
    handleKeydown(e) {
        if (!this.resultsContainer.classList.contains('show')) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.currentResults.length - 1);
                this.updateSelection();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelection();
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && this.currentResults[this.selectedIndex]) {
                    this.navigateToGame(this.currentResults[this.selectedIndex].url);
                }
                break;
                
            case 'Escape':
                this.hideResults();
                this.searchInput.blur();
                break;
        }
    }
    
    updateSelection() {
        const items = this.resultsContainer.querySelectorAll('.search-result-item');
        items.forEach((item, index) => {
            item.classList.toggle('selected', index === this.selectedIndex);
        });
    }
    
    navigateToGame(url) {
        // 立即隐藏搜索结果
        this.hideResults();
        
        // 根据当前页面路径调整URL
        const currentPath = window.location.pathname;
        let targetUrl = url;
        
        if (currentPath.includes('/popular_games/') || currentPath.includes('/new_games/')) {
            targetUrl = '../' + url;
        }
        
        // 使用更快的跳转方式
        window.location.assign(targetUrl);
    }
    
    showResults() {
        this.resultsContainer.classList.add('show');
    }
    
    hideResults() {
        this.resultsContainer.classList.remove('show');
        this.selectedIndex = -1;
    }
}

/**
 * 禁用导航拦截功能
 */
function initDisabledNavigation() {
    // 监听所有导航链接的点击事件
    document.addEventListener('click', function(e) {
        // 检查是否点击了被禁用的导航链接
        if (e.target.classList.contains('nav-disabled')) {
            e.preventDefault(); // 阻止默认跳转行为

            // 显示开发中提示
            alert('该功能正在开发中，敬请期待！');

            console.log('🚧 Disabled navigation clicked:', e.target.textContent);
        }
    });

    console.log('🔒 Disabled navigation interceptor initialized');
}

/**
 * Game Feature 折叠功能类
 */
class FeatureCollapse {
    constructor() {
        this.collapseElements = document.querySelectorAll('.feature-collapsible');
        this.init();
    }

    init() {
        if (this.collapseElements.length === 0) {
            return;
        }

        this.collapseElements.forEach(element => {
            this.initCollapseElement(element);
        });

        console.log(`📋 Feature collapse initialized for ${this.collapseElements.length} elements`);
    }

    initCollapseElement(element) {
        // 适配两种结构：
        // 1) 旧结构：.feature-collapsible 内部包含 .feature-expand-btn
        // 2) 新结构：.feature-content-wrapper > (.feature-collapsible + .feature-expand-control)
        const wrapper = element.closest('.feature-content-wrapper') || element;
        const expandBtn = wrapper.querySelector('.feature-expand-btn') || element.querySelector('.feature-expand-btn');

        if (!expandBtn) {
            console.warn('⚠️ Feature expand button not found');
            return;
        }

        // 记录初始折叠高度（来自 CSS 的 max-height），用于折叠时还原
        const inner = element.querySelector('.feature-collapsible');
        const targetEl = inner || element; // 兼容两种结构
        const computed = window.getComputedStyle(targetEl);
        const computedMax = computed.maxHeight;
        let collapsedPx = 1000;
        if (computedMax && computedMax.endsWith('px')) {
            const v = parseInt(computedMax, 10);
            if (!Number.isNaN(v)) collapsedPx = v;
        }
        targetEl.dataset.collapsedHeight = String(collapsedPx);

        // 绑定点击事件
        expandBtn.addEventListener('click', () => {
            this.toggleExpand(element, expandBtn);
        });

        // 键盘支持
        expandBtn.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleExpand(element, expandBtn);
            }
        });
    }

    toggleExpand(element, expandBtn) {
        const isExpanded = expandBtn.getAttribute('data-expanded') === 'true';
        const newExpandedState = !isExpanded;

        // 更新状态
        expandBtn.setAttribute('data-expanded', newExpandedState.toString());
        expandBtn.setAttribute('aria-expanded', newExpandedState.toString());

        // 切换CSS类
        if (newExpandedState) {
            element.classList.add('expanded');
        } else {
            element.classList.remove('expanded');
        }

        // 如果结构是 wrapper > .feature-collapsible，需要对内层设置 max-height
        const inner = element.querySelector('.feature-collapsible');
        const targetEl = inner || element; // 兼容旧结构
        if (newExpandedState) {
            // 展开：取消限制
            targetEl.style.maxHeight = 'none';
        } else {
            // 折叠：还原到初始 CSS 定义的折叠高度（避免越来越高）
            const collapsedPx = parseInt(targetEl.dataset.collapsedHeight || '1000', 10);
            targetEl.style.maxHeight = `${collapsedPx}px`;
        }

        // 更新按钮文字
        const expandText = expandBtn.querySelector('.expand-text');
        if (expandText) {
            expandText.textContent = newExpandedState ? 'Show Less' : 'Show More';
        }

        // 滚动到合适位置（折叠时）
        if (!newExpandedState) {
            setTimeout(() => {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        }

        console.log(`📋 Feature ${newExpandedState ? 'expanded' : 'collapsed'}`);
    }
}

// 初始化搜索功能
document.addEventListener('DOMContentLoaded', function() {
    // 等待游戏数据加载完成
    if (typeof window.GAMES_DATA !== 'undefined') {
        new GameSearch();
    } else {
        console.warn('⚠️ Games data not loaded, search functionality disabled');
    }
});