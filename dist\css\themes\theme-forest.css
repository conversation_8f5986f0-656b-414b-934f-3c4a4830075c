/* GamePort - Forest Theme (森林冒险 - 绿色系) */
/* 自然绿意风格，温馨森林探险氛围 */

:root {
  /* 主要颜色变量 - 绿色系 */
  --primary-color: #059669;
  --primary-color-2: #059669;
  --primary-hover: #047857;
  --primary-light: #d1fae5;
  --secondary-color: #065f46;
  --accent-color: #84cc16;
  
  /* 背景颜色 */
  --bg-primary: #f0fdf4;
  --bg-secondary: #dcfce7;
  --bg-tertiary: #bbf7d0;
  --bg-quaternary: #f8f9fa;
  --bg-info: #bbf7d0;
  --bg-dark: #14532d;
  
  /* Header专用背景色 - 比主背景更深 */
  --header-bg-color: #ecfdf5;
  
  /* 文字颜色 */
  --text-primary: #14532d;
  --text-secondary: #166534;
  --text-light: #22c55e;
  --text-white: #ffffff;
  
  /* 辅助颜色变量 */
  --border-color: #bbf7d0;
  --border-hover: #86efac;
  --shadow-color: rgba(5, 150, 105, 0.15);
  --shadow-hover: rgba(5, 150, 105, 0.25);
  
  /* 状态颜色 */
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --info-color: #0891b2;
  
  /* 按钮颜色状态 */
  --btn-primary-bg: #059669;
  --btn-primary-hover: #047857;
  --btn-primary-active: #065f46;
  --btn-secondary-bg: #84cc16;
  --btn-secondary-hover: #65a30d;
  
  /* 链接颜色 */
  --link-color: #059669;
  --link-hover: #047857;
  --link-visited: #065f46;
  
  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #059669 0%, #047857 100%);
  --gradient-secondary: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}
