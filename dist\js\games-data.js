// 游戏搜索数据 - 构建时自动生成
// 请勿手动修改此文件
window.GAMES_DATA = [
  {
    "id": "super-mario-bros",
    "name": "Super Mario Bros",
    "category": "popular",
    "thumbnail": "images/popular_games_image/super-mario-bros.png",
    "url": "popular_games/super-mario-bros.html"
  },
  {
    "id": "pac-man",
    "name": "Pac-Man",
    "category": "popular",
    "thumbnail": "images/popular_games_image/pac-man.png",
    "url": "popular_games/pac-man.html"
  },
  {
    "id": "tetris",
    "name": "Tetris",
    "category": "popular",
    "thumbnail": "images/popular_games_image/tetris.png",
    "url": "popular_games/tetris.html"
  },
  {
    "id": "space-invaders",
    "name": "Space Invaders",
    "category": "new",
    "thumbnail": "images/new_games_image/space-invaders.png",
    "url": "new_games/space-invaders.html"
  },
  {
    "id": "frogger",
    "name": "Frogger",
    "category": "new",
    "thumbnail": "images/new_games_image/frogger.png",
    "url": "new_games/frogger.html"
  },
  {
    "id": "snake",
    "name": "Snake",
    "category": "new",
    "thumbnail": "images/new_games_image/snake.png",
    "url": "new_games/snake.html"
  }
];

// 搜索数据统计
console.log('🎮 Loaded', window.GAMES_DATA.length, 'games for search');
